// ===== TANCOMO SHIPPING SERVICES - MAIN JAVASCRIPT =====

class TancomoWebsite {
    constructor() {
        this.currentLanguage = localStorage.getItem('language') || 'en';
        this.currentTheme = localStorage.getItem('theme') || 'light';
        this.isLoading = true;
        this.scrollPosition = 0;
        
        this.init();
    }
    
    init() {
        // Wait for DOM to be ready
        if (document.readyState === 'loading') {
            document.addEventListener('DOMContentLoaded', () => this.initializeApp());
        } else {
            this.initializeApp();
        }
    }
    
    initializeApp() {
        this.setupTheme();
        this.setupLanguage();
        this.setupEventListeners();
        this.setupStickyHeader();
        this.setupBackToTop();
        this.setupLoadingScreen();
        this.setupForms();
        this.setupSmoothScrolling();
        this.setupAnimations();
        this.setupHeroSlideshow();
        this.setupMaritimeBackground();
        this.setupLogoClickToTop();

        // Initialize page-specific functionality
        this.initializePageSpecific();
    }
    
    // ===== THEME MANAGEMENT =====
    setupTheme() {
        document.documentElement.setAttribute('data-theme', this.currentTheme);
        this.updateThemeToggle();
    }
    
    updateThemeToggle() {
        const themeToggle = document.getElementById('theme-toggle');
        if (themeToggle) {
            themeToggle.setAttribute('aria-label', 
                this.currentTheme === 'light' ? 'Switch to dark mode' : 'Switch to light mode'
            );
        }
    }
    
    toggleTheme() {
        this.currentTheme = this.currentTheme === 'light' ? 'dark' : 'light';
        document.documentElement.setAttribute('data-theme', this.currentTheme);
        localStorage.setItem('theme', this.currentTheme);
        this.updateThemeToggle();
        
        // Add transition class temporarily
        document.body.style.transition = 'background-color 0.3s ease, color 0.3s ease';
        setTimeout(() => {
            document.body.style.transition = '';
        }, 300);
    }

    // ===== HERO SLIDESHOW =====
    setupHeroSlideshow() {
        this.currentSlide = 0;
        this.slides = document.querySelectorAll('.hero-slide');
        this.indicators = document.querySelectorAll('.indicator');
        this.slideInterval = null;

        if (this.slides.length === 0) return;

        // Set initial background images
        this.slides.forEach((slide, index) => {
            const bgImage = slide.getAttribute('data-bg');
            if (bgImage) {
                slide.style.backgroundImage = `url('${bgImage}')`;
            }
        });

        // Setup indicator click handlers
        this.indicators.forEach((indicator, index) => {
            indicator.addEventListener('click', () => {
                this.goToSlide(index);
            });
        });

        // Start automatic slideshow
        this.startSlideshow();

        // Pause on hover
        const heroSection = document.querySelector('.hero');
        if (heroSection) {
            heroSection.addEventListener('mouseenter', () => this.pauseSlideshow());
            heroSection.addEventListener('mouseleave', () => this.startSlideshow());
        }
    }

    goToSlide(slideIndex) {
        if (slideIndex < 0 || slideIndex >= this.slides.length) return;

        // Remove active class from current slide and indicator
        this.slides[this.currentSlide].classList.remove('active');
        this.indicators[this.currentSlide].classList.remove('active');

        // Set new current slide
        this.currentSlide = slideIndex;

        // Add active class to new slide and indicator
        this.slides[this.currentSlide].classList.add('active');
        this.indicators[this.currentSlide].classList.add('active');
    }

    nextSlide() {
        const nextIndex = (this.currentSlide + 1) % this.slides.length;
        this.goToSlide(nextIndex);
    }

    startSlideshow() {
        this.pauseSlideshow(); // Clear any existing interval
        this.slideInterval = setInterval(() => {
            this.nextSlide();
        }, 6000); // 6 seconds per slide
    }

    pauseSlideshow() {
        if (this.slideInterval) {
            clearInterval(this.slideInterval);
            this.slideInterval = null;
        }
    }

    destroySlideshow() {
        this.pauseSlideshow();
        this.slides = null;
        this.indicators = null;
        this.currentSlide = 0;
    }

    // ===== MARITIME BACKGROUND =====
    setupMaritimeBackground() {
        this.scrollY = 0;
        this.ticking = false;

        // Get all scroll elements
        this.scrollElements = document.querySelectorAll('.scroll-element');
        this.waves = document.querySelectorAll('.wave');

        if (this.scrollElements.length === 0) return;

        // Bind scroll event
        this.handleScroll = this.handleScroll.bind(this);
        this.updateScrollAnimation = this.updateScrollAnimation.bind(this);

        window.addEventListener('scroll', this.handleScroll);

        // Initialize animation
        this.updateScrollAnimation();
    }

    handleScroll() {
        if (!this.ticking) {
            requestAnimationFrame(this.updateScrollAnimation);
            this.ticking = true;
        }
    }

    updateScrollAnimation() {
        this.scrollY = window.pageYOffset;

        // Apply scroll-based transformations to maritime elements
        this.scrollElements.forEach((element, index) => {
            const speed = (index + 1) * 0.1; // Different speeds for each element
            const yPos = -(this.scrollY * speed);
            element.style.transform = `translateY(${yPos}px)`;
        });

        // Parallax effect for waves
        this.waves.forEach((wave, index) => {
            const speed = (index + 1) * 0.05;
            const yPos = this.scrollY * speed;
            wave.style.transform = `translateY(${yPos}px)`;
        });

        this.ticking = false;
    }

    destroyMaritimeBackground() {
        if (this.handleScroll) {
            window.removeEventListener('scroll', this.handleScroll);
        }
        this.scrollElements = null;
        this.waves = null;
    }

    // ===== LOGO CLICK TO TOP =====
    setupLogoClickToTop() {
        const logos = document.querySelectorAll('.logo, .footer-logo');

        logos.forEach(logo => {
            logo.style.cursor = 'pointer';
            logo.addEventListener('click', (e) => {
                e.preventDefault();
                window.scrollTo({
                    top: 0,
                    behavior: 'smooth'
                });
            });
        });
    }

    // ===== LANGUAGE MANAGEMENT =====
    setupLanguage() {
        this.translatePage();
        this.updateLanguageToggle();
    }
    
    updateLanguageToggle() {
        const currentFlag = document.getElementById('current-flag');
        const currentLang = document.getElementById('current-lang');
        
        if (currentFlag && currentLang) {
            if (this.currentLanguage === 'en') {
                currentFlag.src = 'assets/images/flags/en.png';
                currentFlag.alt = 'English';
                currentLang.textContent = 'EN';
            } else {
                currentFlag.src = 'assets/images/flags/tz.png';
                currentFlag.alt = 'Kiswahili';
                currentLang.textContent = 'SW';
            }
        }
    }
    
    switchLanguage(lang) {
        if (lang !== this.currentLanguage) {
            this.currentLanguage = lang;
            localStorage.setItem('language', lang);
            this.translatePage();
            this.updateLanguageToggle();
            this.showMessage('Language changed successfully', 'success');
        }
    }
    
    translatePage() {
        const elements = document.querySelectorAll('[data-translate]');
        elements.forEach(element => {
            const key = element.getAttribute('data-translate');
            const translation = translations[this.currentLanguage]?.[key];
            
            if (translation) {
                if (element.tagName === 'INPUT' || element.tagName === 'TEXTAREA') {
                    element.placeholder = translation;
                } else if (element.tagName === 'OPTION') {
                    element.textContent = translation;
                } else {
                    element.textContent = translation;
                }
            }
        });
        
        // Update document language
        document.documentElement.lang = this.currentLanguage;
    }
    
    // ===== EVENT LISTENERS =====
    setupEventListeners() {
        // Theme toggle
        const themeToggle = document.getElementById('theme-toggle');
        if (themeToggle) {
            themeToggle.addEventListener('click', () => this.toggleTheme());
        }
        
        // Language switcher
        const langBtn = document.getElementById('lang-btn');
        const langDropdown = document.getElementById('lang-dropdown');
        
        if (langBtn && langDropdown) {
            langBtn.addEventListener('click', (e) => {
                e.stopPropagation();
                langDropdown.classList.toggle('show');
            });
            
            // Close dropdown when clicking outside
            document.addEventListener('click', () => {
                langDropdown.classList.remove('show');
            });
            
            // Language options
            const langOptions = document.querySelectorAll('.lang-option');
            langOptions.forEach(option => {
                option.addEventListener('click', (e) => {
                    const lang = e.currentTarget.getAttribute('data-lang');
                    this.switchLanguage(lang);
                    langDropdown.classList.remove('show');
                });
            });
        }
        
        // Mobile menu toggle
        const mobileMenuToggle = document.getElementById('mobile-menu-toggle');
        const navMenu = document.getElementById('nav-menu');

        if (mobileMenuToggle && navMenu) {
            // Toggle mobile menu
            mobileMenuToggle.addEventListener('click', (e) => {
                e.preventDefault();
                e.stopPropagation();
                mobileMenuToggle.classList.toggle('active');
                navMenu.classList.toggle('show');
            });

            // Close mobile menu when clicking on links
            const navLinks = navMenu.querySelectorAll('.nav-link');
            navLinks.forEach(link => {
                link.addEventListener('click', () => {
                    mobileMenuToggle.classList.remove('active');
                    navMenu.classList.remove('show');
                });
            });

            // Close mobile menu when clicking outside
            document.addEventListener('click', (e) => {
                if (!navMenu.contains(e.target) && !mobileMenuToggle.contains(e.target)) {
                    if (navMenu.classList.contains('show')) {
                        mobileMenuToggle.classList.remove('active');
                        navMenu.classList.remove('show');
                    }
                }
            });

            // Close mobile menu on window resize to desktop
            window.addEventListener('resize', () => {
                if (window.innerWidth >= 1024 && navMenu.classList.contains('show')) {
                    mobileMenuToggle.classList.remove('active');
                    navMenu.classList.remove('show');
                }
            });
        }
        
        // Quote modal
        this.setupQuoteModal();
        
        // Window events
        window.addEventListener('scroll', () => this.handleScroll());
        window.addEventListener('resize', () => this.handleResize());
    }
    
    // ===== QUOTE MODAL =====
    setupQuoteModal() {
        const quoteButtons = document.querySelectorAll('.quote-btn, #hero-quote-btn, #cta-quote-btn');
        const quoteModal = document.getElementById('quote-modal');
        const quoteModalClose = document.getElementById('quote-modal-close');
        
        if (quoteModal) {
            // Open modal
            quoteButtons.forEach(btn => {
                btn.addEventListener('click', () => {
                    quoteModal.classList.add('show');
                    document.body.style.overflow = 'hidden';
                });
            });
            
            // Close modal
            if (quoteModalClose) {
                quoteModalClose.addEventListener('click', () => {
                    this.closeQuoteModal();
                });
            }
            
            // Close on backdrop click
            quoteModal.addEventListener('click', (e) => {
                if (e.target === quoteModal) {
                    this.closeQuoteModal();
                }
            });
            
            // Close on escape key
            document.addEventListener('keydown', (e) => {
                if (e.key === 'Escape' && quoteModal.classList.contains('show')) {
                    this.closeQuoteModal();
                }
            });
        }
    }
    
    closeQuoteModal() {
        const quoteModal = document.getElementById('quote-modal');
        if (quoteModal) {
            quoteModal.classList.remove('show');
            document.body.style.overflow = '';
        }
    }
    
    // ===== STICKY HEADER =====
    setupStickyHeader() {
        this.lastScrollTop = 0;
        this.header = document.getElementById('main-header');
    }
    
    handleScroll() {
        const scrollTop = window.pageYOffset || document.documentElement.scrollTop;
        
        // Update scroll position for other functions
        this.scrollPosition = scrollTop;
        
        // Sticky header behavior
        if (this.header) {
            if (scrollTop > 100) {
                this.header.classList.add('scrolled');
                
                // Hide/show header based on scroll direction
                if (scrollTop > this.lastScrollTop && scrollTop > 200) {
                    this.header.style.transform = 'translateY(-100%)';
                } else {
                    this.header.style.transform = 'translateY(0)';
                }
            } else {
                this.header.classList.remove('scrolled');
                this.header.style.transform = 'translateY(0)';
            }
        }
        
        this.lastScrollTop = scrollTop;
        
        // Update back to top button
        this.updateBackToTop();
        
        // Update navigation active states
        this.updateActiveNavigation();
    }
    
    // ===== BACK TO TOP BUTTON =====
    setupBackToTop() {
        const backToTopBtn = document.getElementById('back-to-top');
        if (backToTopBtn) {
            backToTopBtn.addEventListener('click', () => {
                window.scrollTo({
                    top: 0,
                    behavior: 'smooth'
                });
            });
        }
    }
    
    updateBackToTop() {
        const backToTopBtn = document.getElementById('back-to-top');
        if (backToTopBtn) {
            if (this.scrollPosition > 300) {
                backToTopBtn.classList.add('show');
            } else {
                backToTopBtn.classList.remove('show');
            }
        }
    }
    
    // ===== LOADING SCREEN =====
    setupLoadingScreen() {
        const loadingScreen = document.getElementById('loading-screen');
        
        // Simulate loading time
        setTimeout(() => {
            if (loadingScreen) {
                loadingScreen.classList.add('hidden');
                setTimeout(() => {
                    loadingScreen.style.display = 'none';
                }, 300);
            }
            this.isLoading = false;
        }, 1000);
    }
    
    // ===== FORM HANDLING =====
    setupForms() {
        // Quote form
        const quoteForm = document.getElementById('quote-form');
        if (quoteForm) {
            quoteForm.addEventListener('submit', (e) => this.handleQuoteForm(e));
        }
        
        // Contact form
        const contactForm = document.getElementById('contact-form');
        if (contactForm) {
            contactForm.addEventListener('submit', (e) => this.handleContactForm(e));
        }
        
        // Real-time validation
        this.setupFormValidation();
    }
    
    setupFormValidation() {
        const forms = document.querySelectorAll('form');
        forms.forEach(form => {
            const inputs = form.querySelectorAll('input, textarea, select');
            inputs.forEach(input => {
                input.addEventListener('blur', () => this.validateField(input));
                input.addEventListener('input', () => this.clearFieldError(input));
            });
        });
    }
    
    validateField(field) {
        const value = field.value.trim();
        const isRequired = field.hasAttribute('required');
        const type = field.type;
        let isValid = true;
        let errorMessage = '';
        
        // Required field validation
        if (isRequired && !value) {
            isValid = false;
            errorMessage = 'This field is required';
        }
        
        // Email validation
        if (type === 'email' && value) {
            const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
            if (!emailRegex.test(value)) {
                isValid = false;
                errorMessage = 'Please enter a valid email address';
            }
        }
        
        // Phone validation
        if (type === 'tel' && value) {
            const phoneRegex = /^[\+]?[0-9\s\-\(\)]{10,}$/;
            if (!phoneRegex.test(value)) {
                isValid = false;
                errorMessage = 'Please enter a valid phone number';
            }
        }
        
        this.showFieldError(field, isValid, errorMessage);
        return isValid;
    }
    
    showFieldError(field, isValid, message) {
        const errorElement = field.parentNode.querySelector('.error-message');
        
        if (isValid) {
            field.classList.remove('error');
            if (errorElement) {
                errorElement.textContent = '';
                errorElement.classList.remove('show');
            }
        } else {
            field.classList.add('error');
            if (errorElement) {
                errorElement.textContent = message;
                errorElement.classList.add('show');
            }
        }
    }
    
    clearFieldError(field) {
        field.classList.remove('error');
        const errorElement = field.parentNode.querySelector('.error-message');
        if (errorElement) {
            errorElement.classList.remove('show');
        }
    }

    handleQuoteForm(e) {
        e.preventDefault();

        const form = e.target;
        const formData = new FormData(form);
        const data = Object.fromEntries(formData);

        // Validate all fields
        const inputs = form.querySelectorAll('input, textarea, select');
        let isFormValid = true;

        inputs.forEach(input => {
            if (!this.validateField(input)) {
                isFormValid = false;
            }
        });

        if (isFormValid) {
            // Simulate form submission
            this.showMessage('Quote request sent successfully! We will contact you soon.', 'success');
            form.reset();
            this.closeQuoteModal();

            // In a real application, you would send the data to your server
        } else {
            this.showMessage('Please correct the errors in the form.', 'error');
        }
    }

    handleContactForm(e) {
        e.preventDefault();

        const form = e.target;
        const formData = new FormData(form);
        const data = Object.fromEntries(formData);

        // Validate all fields
        const inputs = form.querySelectorAll('input, textarea, select');
        let isFormValid = true;

        inputs.forEach(input => {
            if (!this.validateField(input)) {
                isFormValid = false;
            }
        });

        if (isFormValid) {
            // Simulate form submission
            this.showMessage('Message sent successfully! We will get back to you soon.', 'success');
            form.reset();

            // In a real application, you would send the data to your server
        } else {
            this.showMessage('Please correct the errors in the form.', 'error');
        }
    }

    // ===== SMOOTH SCROLLING =====
    setupSmoothScrolling() {
        const links = document.querySelectorAll('a[href^="#"]');
        links.forEach(link => {
            link.addEventListener('click', (e) => {
                const href = link.getAttribute('href');
                if (href === '#') return;

                e.preventDefault();
                const target = document.querySelector(href);

                if (target) {
                    const headerHeight = this.header ? this.header.offsetHeight : 80;
                    const targetPosition = target.offsetTop - headerHeight - 20;

                    window.scrollTo({
                        top: targetPosition,
                        behavior: 'smooth'
                    });
                }
            });
        });
    }

    // ===== NAVIGATION ACTIVE STATES =====
    updateActiveNavigation() {
        const sections = document.querySelectorAll('section[id]');
        const navLinks = document.querySelectorAll('.nav-link');

        let currentSection = '';
        const headerHeight = this.header ? this.header.offsetHeight : 80;

        sections.forEach(section => {
            const sectionTop = section.offsetTop - headerHeight - 100;
            const sectionHeight = section.offsetHeight;

            if (this.scrollPosition >= sectionTop &&
                this.scrollPosition < sectionTop + sectionHeight) {
                currentSection = section.getAttribute('id');
            }
        });

        navLinks.forEach(link => {
            link.classList.remove('active');
            const href = link.getAttribute('href');

            if (href === `#${currentSection}` ||
                (href.includes('#') && href.split('#')[1] === currentSection)) {
                link.classList.add('active');
            }
        });
    }

    // ===== ANIMATIONS =====
    setupAnimations() {
        // Intersection Observer for scroll animations
        const observerOptions = {
            threshold: 0.1,
            rootMargin: '0px 0px -50px 0px'
        };

        const observer = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    entry.target.classList.add('animate-fade-in-up');
                }
            });
        }, observerOptions);

        // Observe elements for animation
        const animateElements = document.querySelectorAll(
            '.service-card, .partner-card, .feature-item, .team-member, .certification-item, .mvv-card'
        );

        animateElements.forEach(el => {
            observer.observe(el);
        });
    }

    // ===== MESSAGE SYSTEM =====
    showMessage(message, type = 'info') {
        const container = document.getElementById('message-container');
        if (!container) return;

        const messageEl = document.createElement('div');
        messageEl.className = `message ${type}`;
        messageEl.innerHTML = `
            <span>${message}</span>
            <button class="message-close" aria-label="Close message">&times;</button>
        `;

        container.appendChild(messageEl);

        // Show message
        setTimeout(() => {
            messageEl.classList.add('show');
        }, 100);

        // Auto remove after 5 seconds
        setTimeout(() => {
            this.removeMessage(messageEl);
        }, 5000);

        // Close button
        const closeBtn = messageEl.querySelector('.message-close');
        closeBtn.addEventListener('click', () => {
            this.removeMessage(messageEl);
        });
    }

    removeMessage(messageEl) {
        messageEl.classList.remove('show');
        setTimeout(() => {
            if (messageEl.parentNode) {
                messageEl.parentNode.removeChild(messageEl);
            }
        }, 300);
    }

    // ===== UTILITY METHODS =====
    handleResize() {
        // Handle window resize events
        this.updateActiveNavigation();
    }

    initializePageSpecific() {
        // Initialize page-specific functionality based on current page
        const path = window.location.pathname;

        if (path.includes('gallery.html')) {
            // Gallery page specific initialization will be handled by gallery.js
        }

        // Add any other page-specific initialization here
    }

    // ===== PUBLIC API =====
    // Methods that can be called from outside
    scrollToTop() {
        window.scrollTo({
            top: 0,
            behavior: 'smooth'
        });
    }

    scrollToSection(sectionId) {
        const target = document.getElementById(sectionId);
        if (target) {
            const headerHeight = this.header ? this.header.offsetHeight : 80;
            const targetPosition = target.offsetTop - headerHeight - 20;

            window.scrollTo({
                top: targetPosition,
                behavior: 'smooth'
            });
        }
    }

    openQuoteModal() {
        const quoteModal = document.getElementById('quote-modal');
        if (quoteModal) {
            quoteModal.classList.add('show');
            document.body.style.overflow = 'hidden';
        }
    }

    setLanguage(lang) {
        this.switchLanguage(lang);
    }

    setTheme(theme) {
        if (theme === 'light' || theme === 'dark') {
            this.currentTheme = theme;
            document.documentElement.setAttribute('data-theme', this.currentTheme);
            localStorage.setItem('theme', this.currentTheme);
            this.updateThemeToggle();
        }
    }

    // Slideshow control methods
    goToSlidePublic(slideIndex) {
        if (this.goToSlide) {
            this.goToSlide(slideIndex);
        }
    }

    pauseSlideshowPublic() {
        if (this.pauseSlideshow) {
            this.pauseSlideshow();
        }
    }

    startSlideshowPublic() {
        if (this.startSlideshow) {
            this.startSlideshow();
        }
    }
}

// ===== INITIALIZE APPLICATION =====
const tancomoWebsite = new TancomoWebsite();

// ===== GLOBAL FUNCTIONS =====
// Make some functions available globally for inline event handlers if needed
window.tancomoWebsite = tancomoWebsite;

// ===== PERFORMANCE OPTIMIZATION =====
// Lazy loading for images
if ('IntersectionObserver' in window) {
    const imageObserver = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                const img = entry.target;
                img.src = img.dataset.src || img.src;
                img.classList.remove('lazy');
                imageObserver.unobserve(img);
            }
        });
    });

    // Observe all images with loading="lazy"
    document.querySelectorAll('img[loading="lazy"]').forEach(img => {
        imageObserver.observe(img);
    });
}

// ===== ERROR HANDLING =====
window.addEventListener('error', (e) => {
    console.error('JavaScript error:', e.error);
    // In production, you might want to send this to an error tracking service
});

window.addEventListener('unhandledrejection', (e) => {
    console.error('Unhandled promise rejection:', e.reason);
    // In production, you might want to send this to an error tracking service
});

// ===== ACCESSIBILITY ENHANCEMENTS =====
// Skip to main content functionality
document.addEventListener('keydown', (e) => {
    if (e.key === 'Tab' && !e.shiftKey) {
        const focusedElement = document.activeElement;
        if (focusedElement && focusedElement.classList.contains('skip-link')) {
            e.preventDefault();
            const target = document.querySelector(focusedElement.getAttribute('href'));
            if (target) {
                target.focus();
            }
        }
    }
});
